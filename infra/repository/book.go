package repository

import (
	"context"
	"shelf/domain/entity"
	"shelf/domain/repository"
	"shelf/infra/repository/dao"
	v1 "shelf/interface/proto/v1"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/ioc"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

func init() {
	ioc.ProvideFunc[repository.BookRepository](NewBookRepository)
}

type bookRepository struct{}

func NewBookRepository() repository.BookRepository {
	return new(bookRepository)
}

func (s *bookRepository) CreateBook(ctx context.Context, book *entity.Book) (*entity.Book, error) {
	ctx = xgorm.NewContext(ctx, ins)
	t := xtype.NewTimestamp(time.Now())
	book.CreateTime = t
	book.UpdateTime = t
	err := dao.CreateBook(ctx, (*v1.Book)(book))
	if err != nil {
		return nil, err
	}
	return book, nil
}
