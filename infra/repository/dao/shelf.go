package dao

import (
	"context"
	v1 "shelf/interface/proto/v1"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
)

type Shelf struct {
	ID         int64     `gorm:"primaryKey;autoIncrement"`
	Name       string    `gorm:"size:64;uniqueIndex:uni_name"`
	Theme      string    `gorm:"size:64"`
	CreateTime time.Time `gorm:"type:datetime(3)"`
	UpdateTime time.Time `gorm:"type:datetime(3)"`
}

func CreateShelf(ctx context.Context, shelf *Shelf) error {
	return xgorm.FromContext(ctx).
		Create(shelf).
		Error
}

func ListShelf(ctx context.Context) ([]*Shelf, error) {
	var shelves []*Shelf
	err := xgorm.FromContext(ctx).
		Find(&shelves).
		Error
	if err != nil {
		return nil, err
	}
	return shelves, nil
}

func ListShelfBooks(ctx context.Context, shelfID int64) ([]*v1.Book, error) {
	var books []*v1.Book
	err := xgorm.FromContext(ctx).
		Where("shelf_id = ?", shelfID).
		Find(&books).
		Error
	if err != nil {
		return nil, err
	}
	return books, nil
}
