package repository

import (
	"context"
	"time"

	"shelf/domain/entity"
	"shelf/domain/repository"
	"shelf/infra/repository/dao"
	"shelf/infra/repository/mapper"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/ioc"
)

func init() {
	ioc.ProvideFunc[repository.ShelfRepository](NewShelfRepository)
}

type shelfRepository struct{}

func NewShelfRepository() repository.ShelfRepository {
	return new(shelfRepository)
}

func (s *shelfRepository) CreateShelf(ctx context.Context, shelf *entity.Shelf) (*entity.Shelf, error) {
	ctx = xgorm.NewContext(ctx, ins)
	t := time.Now().Truncate(time.Second)
	po := mapper.FromShelf(shelf)
	po.CreateTime = t
	po.UpdateTime = t
	err := dao.CreateShelf(ctx, po)
	if err != nil {
		return nil, err
	}
	return mapper.ToShelf(po), nil
}

func (s *shelfRepository) ListShelf(ctx context.Context) ([]*entity.Shelf, error) {
	ctx = xgorm.NewContext(ctx, ins)
	po, err := dao.ListShelf(ctx)
	if err != nil {
		return nil, err
	}
	shelves := make([]*entity.Shelf, len(po))
	for i, shelf := range po {
		shelves[i] = mapper.ToShelf(
			shelf)
	}
	return shelves, nil
}

func (s *shelfRepository) ListShelfBooks(ctx context.Context, shelfID int64) ([]*entity.Book, error) {
	ctx = xgorm.NewContext(ctx, ins)
	po, err := dao.ListShelfBooks(ctx, shelfID)
	if err != nil {
		return nil, err
	}
	books := make([]*entity.Book, len(po))
	for i, book := range po {
		books[i] = (*entity.Book)(book)
	}
	return books, nil
}
