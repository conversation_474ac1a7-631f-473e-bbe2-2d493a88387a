package mapper

import (
	"shelf/domain/entity"
	"shelf/infra/repository/dao"
)

func FromShelf(shelf *entity.Shelf) *dao.Shelf {
	return &dao.Shelf{
		ID:         shelf.ID,
		Name:       shelf.Name,
		Theme:      shelf.Theme,
		CreateTime: shelf.CreateTime,
		UpdateTime: shelf.UpdateTime,
	}
}

func ToShelf(shelf *dao.Shelf) *entity.Shelf {
	return &entity.Shelf{
		ID:         shelf.ID,
		Name:       shelf.Name,
		Theme:      shelf.Theme,
		CreateTime: shelf.CreateTime,
		UpdateTime: shelf.UpdateTime,
	}
}
