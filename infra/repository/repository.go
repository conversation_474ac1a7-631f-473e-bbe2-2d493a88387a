package repository

import (
	"shelf/infra/repository/dao"
	v1 "shelf/interface/proto/v1"

	"gitlab.papegames.com/fringe/sparrow/pkg/database/xgorm"
	"gitlab.papegames.com/fringe/sparrow/pkg/xdebug"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var ins *xgorm.DB

func Startup() error {
	var err error
	ins, err = xgorm.StdConfig().Build()
	if err != nil {
		xlog.Error("database.Startup with error",
			xlog.Err(err))
		return err
	}

	if xdebug.Debug() {
		err := ins.AutoMigrate(
			new(dao.Shelf),
			new(v1.Book),
		)
		if err != nil {
			xlog.Error("AutoMigrate with error",
				xlog.Err(err))
			return err
		}
		xlog.Debug("AutoMigrate successful")
	}
	return nil
}
