package server

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/log"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/xerror"

	_ "gitlab.papegames.com/fringe/sparrow/pkg/sd/provider/etcd"
)

func Get() []server.Server {
	return []server.Server{
		GetHTTP(),
		GetGRPC(),
	}
}

func Startup() error {
	hooks.Append(log.New())
	return xerror.SerialWithError(
		StartupHTTP,
		StartupGRPC,
	)()
}
