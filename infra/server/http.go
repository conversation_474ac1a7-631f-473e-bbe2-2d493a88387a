package server

import (
	"shelf/infra/config"
	"shelf/interface/control"
	v1 "shelf/interface/proto/v1"

	"gitlab.papegames.com/fringe/sparrow/pkg"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
)

var ginServer = xgin.NewServer()

func GetHTTP() *xgin.Server { return ginServer }

func StartupHTTP() error {
	ginServer.Init(
		server.ServiceName(pkg.AppName),
		server.ServiceHost(config.Get().HostHTTP),
		server.ServiceRegistrar(config.Get().Register),
		server.HTTPServerControl(control.Get()),
		server.HTTPServerRegister(v1.RegisterShelfServiceGinServer),
	)
	return nil
}
