package server

import (
	"google.golang.org/grpc"

	"shelf/infra/config"
	"shelf/interface/control"
	v1 "shelf/interface/proto/v1"

	"gitlab.papegames.com/fringe/sparrow/pkg"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgrpc"
)

var grpcServer = xgrpc.NewServer()

func GetGRPC() *xgrpc.Server { return grpcServer }

func StartupGRPC() error {
	grpcServer.Init(
		server.ServiceName(pkg.AppName),
		server.ServiceHost(config.Get().HostGRPC),
		server.ServiceRegistrar(config.Get().Register),
		server.GRPCServer(control.Get()),
		server.GRPCServerRegister(v1.RegisterShelfServiceServer),
		server.GRPCServerOptions(grpc.ChainUnaryInterceptor(hooks.GetInterceptor()...)),
	)
	return nil
}
