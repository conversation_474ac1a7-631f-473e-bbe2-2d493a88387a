package config

import (
	"sync/atomic"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
)

var conf atomic.Pointer[Config]

func Get() *Config { return conf.Load() }

type Config struct {
	HostHTTP string `xconf:"host_http"`
	HostGRPC string `xconf:"host_grpc"`
	Register bool   `xconf:"register"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	// default config
	c := &Config{
		HostHTTP: "",
		HostGRPC: "",
		Register: false,
	}

	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}

	conf.Store(c)
	return nil
}
