package control

import (
	"context"

	"shelf/application/service"
	v1 "shelf/interface/proto/v1"
)

func (Control) CreateShelf(ctx context.Context, request *v1.CreateShelfRequest) (*v1.<PERSON><PERSON>, error) {
	return service.AppService().
		CreateShelf(ctx, request)
}

func (Control) ListShelf(ctx context.Context, request *v1.ListShelfRequest) (*v1.ListShelfResponse, error) {
	return service.AppService().
		ListShelf(ctx, request)
}

func (Control) ListBook(ctx context.Context, request *v1.ListBookRequest) (*v1.ListBookResponse, error) {
	return service.AppService().
		ListBook(ctx, request)
}
