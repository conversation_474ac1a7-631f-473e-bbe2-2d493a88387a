// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: interface/proto/v1/shelf.proto

package v1

import (
	_ "github.com/google/gnostic/openapiv3"
	_ "gitlab.papegames.com/fringe/protoc-gen-gotag/tagger"
	xtype "gitlab.papegames.com/fringe/sparrow/pkg/xtype"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// A Shelf contains a collection of books with a theme.
type Shelf struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource name of the shelf.
	// Shelf names have the form `shelves/{shelf_id}`.
	// The name is ignored when creating a shelf.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The theme of the shelf
	Theme string `protobuf:"bytes,2,opt,name=theme,proto3" json:"theme,omitempty"`
	// The creation date and time.
	CreateTime *xtype.Timestamp `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The last update date and time.
	UpdateTime    *xtype.Timestamp `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Shelf) Reset() {
	*x = Shelf{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Shelf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Shelf) ProtoMessage() {}

func (x *Shelf) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Shelf.ProtoReflect.Descriptor instead.
func (*Shelf) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{0}
}

func (x *Shelf) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Shelf) GetTheme() string {
	if x != nil {
		return x.Theme
	}
	return ""
}

func (x *Shelf) GetCreateTime() *xtype.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Shelf) GetUpdateTime() *xtype.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// Request message for ShelfService.ListShelf.
type ListShelfRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShelfRequest) Reset() {
	*x = ListShelfRequest{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShelfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShelfRequest) ProtoMessage() {}

func (x *ListShelfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShelfRequest.ProtoReflect.Descriptor instead.
func (*ListShelfRequest) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{1}
}

// Response message for ShelfService.ListShelf.
type ListShelfResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Shelves       []*Shelf               `protobuf:"bytes,1,rep,name=shelves,proto3" json:"shelves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListShelfResponse) Reset() {
	*x = ListShelfResponse{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListShelfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListShelfResponse) ProtoMessage() {}

func (x *ListShelfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListShelfResponse.ProtoReflect.Descriptor instead.
func (*ListShelfResponse) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{2}
}

func (x *ListShelfResponse) GetShelves() []*Shelf {
	if x != nil {
		return x.Shelves
	}
	return nil
}

// Request message for ShelfService.CreateShelf.
type CreateShelfRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The shelf to create.
	Shelf         *Shelf `protobuf:"bytes,1,opt,name=shelf,proto3" json:"shelf,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateShelfRequest) Reset() {
	*x = CreateShelfRequest{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateShelfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateShelfRequest) ProtoMessage() {}

func (x *CreateShelfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateShelfRequest.ProtoReflect.Descriptor instead.
func (*CreateShelfRequest) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{3}
}

func (x *CreateShelfRequest) GetShelf() *Shelf {
	if x != nil {
		return x.Shelf
	}
	return nil
}

type Book struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" gorm:"size:64;primaryKey;autoIncrement"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" gorm:"size:64"`
	ShelfId       int64                  `protobuf:"varint,3,opt,name=shelf_id,json=shelfId,proto3" json:"shelf_id,omitempty" gorm:"size:64"`
	CreateTime    *xtype.Timestamp       `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty" gorm:"type:datetime(3)"`
	UpdateTime    *xtype.Timestamp       `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty" gorm:"type:datetime(3)"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Book) Reset() {
	*x = Book{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Book) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Book) ProtoMessage() {}

func (x *Book) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Book.ProtoReflect.Descriptor instead.
func (*Book) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{4}
}

func (x *Book) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Book) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Book) GetShelfId() int64 {
	if x != nil {
		return x.ShelfId
	}
	return 0
}

func (x *Book) GetCreateTime() *xtype.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Book) GetUpdateTime() *xtype.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type CreateBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Book          *Book                  `protobuf:"bytes,1,opt,name=book,proto3" json:"book,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateBookRequest) Reset() {
	*x = CreateBookRequest{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBookRequest) ProtoMessage() {}

func (x *CreateBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBookRequest.ProtoReflect.Descriptor instead.
func (*CreateBookRequest) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{5}
}

func (x *CreateBookRequest) GetBook() *Book {
	if x != nil {
		return x.Book
	}
	return nil
}

type ListBookRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ShelfId       int64                  `protobuf:"varint,1,opt,name=shelf_id,json=shelfId,proto3" json:"shelf_id,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageToken     string                 `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBookRequest) Reset() {
	*x = ListBookRequest{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookRequest) ProtoMessage() {}

func (x *ListBookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookRequest.ProtoReflect.Descriptor instead.
func (*ListBookRequest) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{6}
}

func (x *ListBookRequest) GetShelfId() int64 {
	if x != nil {
		return x.ShelfId
	}
	return 0
}

func (x *ListBookRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListBookRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

type ListBookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Books         []*Book                `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"`
	NextPageToken string                 `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBookResponse) Reset() {
	*x = ListBookResponse{}
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookResponse) ProtoMessage() {}

func (x *ListBookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_interface_proto_v1_shelf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookResponse.ProtoReflect.Descriptor instead.
func (*ListBookResponse) Descriptor() ([]byte, []int) {
	return file_interface_proto_v1_shelf_proto_rawDescGZIP(), []int{7}
}

func (x *ListBookResponse) GetBooks() []*Book {
	if x != nil {
		return x.Books
	}
	return nil
}

func (x *ListBookResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

var File_interface_proto_v1_shelf_proto protoreflect.FileDescriptor

const file_interface_proto_v1_shelf_proto_rawDesc = "" +
	"\n" +
	"\x1einterface/proto/v1/shelf.proto\x12\x17papegames.sparrow.shelf\x1a\x1cgoogle/api/annotations.proto\x1a\x17google/api/client.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x19google/api/resource.proto\x1a\x1epapegames/type/timestamp.proto\x1a\x1bopenapiv3/annotations.proto\x1a\x13tagger/tagger.proto\"\x87\x02\n" +
	"\x05Shelf\x12\"\n" +
	"\x04name\x18\x01 \x01(\tB\x0e\xe2A\x01\x03\xfaA\a\n" +
	"\x05ShelfR\x04name\x12\"\n" +
	"\x05theme\x18\x02 \x01(\tB\f\xe2A\x01\x02\xbaG\x05x@\x80\x01\x01R\x05theme\x12@\n" +
	"\vcreate_time\x18\x03 \x01(\v2\x19.papegames.type.TimestampB\x04\xe2A\x01\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\x04 \x01(\v2\x19.papegames.type.TimestampB\x04\xe2A\x01\x03R\n" +
	"updateTime:2\xeaA/\n" +
	"\x19shelf.papegames.com/Shelf\x12\x12shelves/{shelf_id}\"\x12\n" +
	"\x10ListShelfRequest\"M\n" +
	"\x11ListShelfResponse\x128\n" +
	"\ashelves\x18\x01 \x03(\v2\x1e.papegames.sparrow.shelf.ShelfR\ashelves\"P\n" +
	"\x12CreateShelfRequest\x12:\n" +
	"\x05shelf\x18\x01 \x01(\v2\x1e.papegames.sparrow.shelf.ShelfB\x04\xe2A\x01\x02R\x05shelf\"\xcf\x02\n" +
	"\x04Book\x12:\n" +
	"\x02id\x18\x01 \x01(\x03B*ҧ\x86\a%gorm:size:64;primaryKey;autoIncrementR\x02id\x12%\n" +
	"\x04name\x18\x02 \x01(\tB\x11ҧ\x86\a\fgorm:size:64R\x04name\x12,\n" +
	"\bshelf_id\x18\x03 \x01(\x03B\x11ҧ\x86\a\fgorm:size:64R\ashelfId\x12Z\n" +
	"\vcreate_time\x18\x04 \x01(\v2\x19.papegames.type.TimestampB\x1e\xe2A\x01\x03ҧ\x86\a\x15gorm:type:datetime(3)R\n" +
	"createTime\x12Z\n" +
	"\vupdate_time\x18\x05 \x01(\v2\x19.papegames.type.TimestampB\x1e\xe2A\x01\x03ҧ\x86\a\x15gorm:type:datetime(3)R\n" +
	"updateTime\"L\n" +
	"\x11CreateBookRequest\x127\n" +
	"\x04book\x18\x01 \x01(\v2\x1d.papegames.sparrow.shelf.BookB\x04\xe2A\x01\x02R\x04book\"h\n" +
	"\x0fListBookRequest\x12\x19\n" +
	"\bshelf_id\x18\x01 \x01(\x03R\ashelfId\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tR\tpageToken\"o\n" +
	"\x10ListBookResponse\x123\n" +
	"\x05books\x18\x01 \x03(\v2\x1d.papegames.sparrow.shelf.BookR\x05books\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken2\xaa\x04\n" +
	"\fShelfService\x12\x84\x01\n" +
	"\tListShelf\x12).papegames.sparrow.shelf.ListShelfRequest\x1a*.papegames.sparrow.shelf.ListShelfResponse\" \xdaA\x05shelf\x82\xd3\xe4\x93\x02\x12\x12\x10/v1/shelves/list\x12~\n" +
	"\vCreateShelf\x12+.papegames.sparrow.shelf.CreateShelfRequest\x1a\x1e.papegames.sparrow.shelf.Shelf\"\"\xdaA\x05shelf\x82\xd3\xe4\x93\x02\x14:\x05shelf\"\v/v1/shelves\x12\x7f\n" +
	"\n" +
	"CreateBook\x12*.papegames.sparrow.shelf.CreateBookRequest\x1a\x1d.papegames.sparrow.shelf.Book\"&\xdaA\x04book\x82\xd3\xe4\x93\x02\x19:\x04book\"\x11/v1/shelves/books\x12z\n" +
	"\bListBook\x12(.papegames.sparrow.shelf.ListBookRequest\x1a).papegames.sparrow.shelf.ListBookResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/v1/shelves/books\x1a\x16\xcaA\x13shelf.papegames.comB>\n" +
	"\x1bcom.papegames.sparrow.shelfB\n" +
	"ShelfProtoP\x01Z\x11shelf/proto/v1;v1b\x06proto3"

var (
	file_interface_proto_v1_shelf_proto_rawDescOnce sync.Once
	file_interface_proto_v1_shelf_proto_rawDescData []byte
)

func file_interface_proto_v1_shelf_proto_rawDescGZIP() []byte {
	file_interface_proto_v1_shelf_proto_rawDescOnce.Do(func() {
		file_interface_proto_v1_shelf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_interface_proto_v1_shelf_proto_rawDesc), len(file_interface_proto_v1_shelf_proto_rawDesc)))
	})
	return file_interface_proto_v1_shelf_proto_rawDescData
}

var file_interface_proto_v1_shelf_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_interface_proto_v1_shelf_proto_goTypes = []any{
	(*Shelf)(nil),              // 0: papegames.sparrow.shelf.Shelf
	(*ListShelfRequest)(nil),   // 1: papegames.sparrow.shelf.ListShelfRequest
	(*ListShelfResponse)(nil),  // 2: papegames.sparrow.shelf.ListShelfResponse
	(*CreateShelfRequest)(nil), // 3: papegames.sparrow.shelf.CreateShelfRequest
	(*Book)(nil),               // 4: papegames.sparrow.shelf.Book
	(*CreateBookRequest)(nil),  // 5: papegames.sparrow.shelf.CreateBookRequest
	(*ListBookRequest)(nil),    // 6: papegames.sparrow.shelf.ListBookRequest
	(*ListBookResponse)(nil),   // 7: papegames.sparrow.shelf.ListBookResponse
	(*xtype.Timestamp)(nil),    // 8: papegames.type.Timestamp
}
var file_interface_proto_v1_shelf_proto_depIdxs = []int32{
	8,  // 0: papegames.sparrow.shelf.Shelf.create_time:type_name -> papegames.type.Timestamp
	8,  // 1: papegames.sparrow.shelf.Shelf.update_time:type_name -> papegames.type.Timestamp
	0,  // 2: papegames.sparrow.shelf.ListShelfResponse.shelves:type_name -> papegames.sparrow.shelf.Shelf
	0,  // 3: papegames.sparrow.shelf.CreateShelfRequest.shelf:type_name -> papegames.sparrow.shelf.Shelf
	8,  // 4: papegames.sparrow.shelf.Book.create_time:type_name -> papegames.type.Timestamp
	8,  // 5: papegames.sparrow.shelf.Book.update_time:type_name -> papegames.type.Timestamp
	4,  // 6: papegames.sparrow.shelf.CreateBookRequest.book:type_name -> papegames.sparrow.shelf.Book
	4,  // 7: papegames.sparrow.shelf.ListBookResponse.books:type_name -> papegames.sparrow.shelf.Book
	1,  // 8: papegames.sparrow.shelf.ShelfService.ListShelf:input_type -> papegames.sparrow.shelf.ListShelfRequest
	3,  // 9: papegames.sparrow.shelf.ShelfService.CreateShelf:input_type -> papegames.sparrow.shelf.CreateShelfRequest
	5,  // 10: papegames.sparrow.shelf.ShelfService.CreateBook:input_type -> papegames.sparrow.shelf.CreateBookRequest
	6,  // 11: papegames.sparrow.shelf.ShelfService.ListBook:input_type -> papegames.sparrow.shelf.ListBookRequest
	2,  // 12: papegames.sparrow.shelf.ShelfService.ListShelf:output_type -> papegames.sparrow.shelf.ListShelfResponse
	0,  // 13: papegames.sparrow.shelf.ShelfService.CreateShelf:output_type -> papegames.sparrow.shelf.Shelf
	4,  // 14: papegames.sparrow.shelf.ShelfService.CreateBook:output_type -> papegames.sparrow.shelf.Book
	7,  // 15: papegames.sparrow.shelf.ShelfService.ListBook:output_type -> papegames.sparrow.shelf.ListBookResponse
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_interface_proto_v1_shelf_proto_init() }
func file_interface_proto_v1_shelf_proto_init() {
	if File_interface_proto_v1_shelf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_interface_proto_v1_shelf_proto_rawDesc), len(file_interface_proto_v1_shelf_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_interface_proto_v1_shelf_proto_goTypes,
		DependencyIndexes: file_interface_proto_v1_shelf_proto_depIdxs,
		MessageInfos:      file_interface_proto_v1_shelf_proto_msgTypes,
	}.Build()
	File_interface_proto_v1_shelf_proto = out.File
	file_interface_proto_v1_shelf_proto_goTypes = nil
	file_interface_proto_v1_shelf_proto_depIdxs = nil
}
