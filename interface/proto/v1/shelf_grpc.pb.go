// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: interface/proto/v1/shelf.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ShelfService_ListShelf_FullMethodName   = "/papegames.sparrow.shelf.ShelfService/ListShelf"
	ShelfService_CreateShelf_FullMethodName = "/papegames.sparrow.shelf.ShelfService/CreateShelf"
	ShelfService_CreateBook_FullMethodName  = "/papegames.sparrow.shelf.ShelfService/CreateBook"
	ShelfService_ListBook_FullMethodName    = "/papegames.sparrow.shelf.ShelfService/ListBook"
)

// ShelfServiceClient is the client API for ShelfService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// This API represents shelf service.
type ShelfServiceClient interface {
	// List shelves.
	ListShelf(ctx context.Context, in *ListShelfRequest, opts ...grpc.CallOption) (*ListShelfResponse, error)
	// Creates a shelf, and returns the new Shelf.
	CreateShelf(ctx context.Context, in *CreateShelfRequest, opts ...grpc.CallOption) (*Shelf, error)
	// Creates a Book to a Shelf.
	CreateBook(ctx context.Context, in *CreateBookRequest, opts ...grpc.CallOption) (*Book, error)
	// List books in a shelf.
	ListBook(ctx context.Context, in *ListBookRequest, opts ...grpc.CallOption) (*ListBookResponse, error)
}

type shelfServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewShelfServiceClient(cc grpc.ClientConnInterface) ShelfServiceClient {
	return &shelfServiceClient{cc}
}

func (c *shelfServiceClient) ListShelf(ctx context.Context, in *ListShelfRequest, opts ...grpc.CallOption) (*ListShelfResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListShelfResponse)
	err := c.cc.Invoke(ctx, ShelfService_ListShelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shelfServiceClient) CreateShelf(ctx context.Context, in *CreateShelfRequest, opts ...grpc.CallOption) (*Shelf, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Shelf)
	err := c.cc.Invoke(ctx, ShelfService_CreateShelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shelfServiceClient) CreateBook(ctx context.Context, in *CreateBookRequest, opts ...grpc.CallOption) (*Book, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Book)
	err := c.cc.Invoke(ctx, ShelfService_CreateBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *shelfServiceClient) ListBook(ctx context.Context, in *ListBookRequest, opts ...grpc.CallOption) (*ListBookResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBookResponse)
	err := c.cc.Invoke(ctx, ShelfService_ListBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShelfServiceServer is the server API for ShelfService service.
// All implementations must embed UnimplementedShelfServiceServer
// for forward compatibility.
//
// This API represents shelf service.
type ShelfServiceServer interface {
	// List shelves.
	ListShelf(context.Context, *ListShelfRequest) (*ListShelfResponse, error)
	// Creates a shelf, and returns the new Shelf.
	CreateShelf(context.Context, *CreateShelfRequest) (*Shelf, error)
	// Creates a Book to a Shelf.
	CreateBook(context.Context, *CreateBookRequest) (*Book, error)
	// List books in a shelf.
	ListBook(context.Context, *ListBookRequest) (*ListBookResponse, error)
	mustEmbedUnimplementedShelfServiceServer()
}

// UnimplementedShelfServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedShelfServiceServer struct{}

func (UnimplementedShelfServiceServer) ListShelf(context.Context, *ListShelfRequest) (*ListShelfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListShelf not implemented")
}
func (UnimplementedShelfServiceServer) CreateShelf(context.Context, *CreateShelfRequest) (*Shelf, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateShelf not implemented")
}
func (UnimplementedShelfServiceServer) CreateBook(context.Context, *CreateBookRequest) (*Book, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBook not implemented")
}
func (UnimplementedShelfServiceServer) ListBook(context.Context, *ListBookRequest) (*ListBookResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBook not implemented")
}
func (UnimplementedShelfServiceServer) mustEmbedUnimplementedShelfServiceServer() {}
func (UnimplementedShelfServiceServer) testEmbeddedByValue()                      {}

// UnsafeShelfServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShelfServiceServer will
// result in compilation errors.
type UnsafeShelfServiceServer interface {
	mustEmbedUnimplementedShelfServiceServer()
}

func RegisterShelfServiceServer(s grpc.ServiceRegistrar, srv ShelfServiceServer) {
	// If the following call pancis, it indicates UnimplementedShelfServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ShelfService_ServiceDesc, srv)
}

func _ShelfService_ListShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListShelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShelfServiceServer).ListShelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShelfService_ListShelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShelfServiceServer).ListShelf(ctx, req.(*ListShelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShelfService_CreateShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateShelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShelfServiceServer).CreateShelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShelfService_CreateShelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShelfServiceServer).CreateShelf(ctx, req.(*CreateShelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShelfService_CreateBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShelfServiceServer).CreateBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShelfService_CreateBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShelfServiceServer).CreateBook(ctx, req.(*CreateBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ShelfService_ListBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBookRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShelfServiceServer).ListBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShelfService_ListBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShelfServiceServer).ListBook(ctx, req.(*ListBookRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ShelfService_ServiceDesc is the grpc.ServiceDesc for ShelfService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ShelfService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "papegames.sparrow.shelf.ShelfService",
	HandlerType: (*ShelfServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListShelf",
			Handler:    _ShelfService_ListShelf_Handler,
		},
		{
			MethodName: "CreateShelf",
			Handler:    _ShelfService_CreateShelf_Handler,
		},
		{
			MethodName: "CreateBook",
			Handler:    _ShelfService_CreateBook_Handler,
		},
		{
			MethodName: "ListBook",
			Handler:    _ShelfService_ListBook_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "interface/proto/v1/shelf.proto",
}
