syntax = "proto3";

package papegames.sparrow.shelf;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "papegames/type/timestamp.proto";
import "openapiv3/annotations.proto";
import "tagger/tagger.proto"; 

option go_package = "shelf/proto/v1;v1";
option java_multiple_files = true;
option java_outer_classname = "ShelfProto";
option java_package = "com.papegames.sparrow.shelf";

// This API represents shelf service.
service ShelfService {
  option (google.api.default_host) = "shelf.papegames.com";

  // List shelves.
  rpc ListShelf(ListShelfRequest) returns (ListShelfResponse) {
    option (google.api.http) = {
      get: "/v1/shelves/list"
    };
    option (google.api.method_signature) = "shelf";
  }

  // Creates a shelf, and returns the new Shelf.
  rpc CreateShelf(CreateShelfRequest) returns (Shelf) {
    option (google.api.http) = {
      post: "/v1/shelves"
      body: "shelf"
    };
    option (google.api.method_signature) = "shelf";
  }

  // Creates a Book to a Shelf.
  rpc CreateBook(CreateBookRequest) returns (Book) {
    option (google.api.http) = {
      post: "/v1/shelves/books"
      body: "book"
    };
    option (google.api.method_signature) = "book";
  }

  // List books in a shelf.
  rpc ListBook(ListBookRequest) returns (ListBookResponse) {
    option (google.api.http) = {
      get: "/v1/shelves/books"
    };
  }

}

// A Shelf contains a collection of books with a theme.
message Shelf {
  option (google.api.resource) = {
    type: "shelf.papegames.com/Shelf",
    pattern: "shelves/{shelf_id}"
  };
  // The resource name of the shelf.
  // Shelf names have the form `shelves/{shelf_id}`.
  // The name is ignored when creating a shelf.
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference).type = "Shelf"
  ];

  // The theme of the shelf
  string theme = 2 [
    (google.api.field_behavior) = REQUIRED,
    (openapi.v3.property) = {
      min_length: 1,
      max_length: 64,
    }
  ];

  // The creation date and time.
  papegames.type.Timestamp create_time = 3
  [(google.api.field_behavior) = OUTPUT_ONLY];

  // The last update date and time.
  papegames.type.Timestamp update_time = 4
  [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for ShelfService.ListShelf.
message ListShelfRequest {
}

// Response message for ShelfService.ListShelf.
message ListShelfResponse {
  repeated Shelf shelves = 1;
}

// Request message for ShelfService.CreateShelf.
message CreateShelfRequest {
  // The shelf to create.
  Shelf shelf = 1 [(google.api.field_behavior) = REQUIRED];
}


message Book { 
  int64 id = 1  [
      (tagger.tags) = "gorm:size:64;primaryKey;autoIncrement"
  ];
  string name = 2 [
      (tagger.tags) = "gorm:size:64"
  ];
   int64 shelf_id = 3 [
      (tagger.tags) = "gorm:size:64"
  ];
  papegames.type.Timestamp create_time = 4
  [(google.api.field_behavior) = OUTPUT_ONLY,
  (tagger.tags) =  "gorm:type:datetime(3)"];
  papegames.type.Timestamp update_time = 5
  [(google.api.field_behavior) = OUTPUT_ONLY,
  (tagger.tags) =  "gorm:type:datetime(3)"];
}

message CreateBookRequest {
  Book book = 1 [(google.api.field_behavior) = REQUIRED];
}

message ListBookRequest {
  int64 shelf_id = 1;
  int32 page_size = 2;
  string page_token = 3;
}

message ListBookResponse {
  repeated Book books = 1;
  string next_page_token = 2;
}