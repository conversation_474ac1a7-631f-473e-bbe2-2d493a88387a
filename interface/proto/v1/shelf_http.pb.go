// Code generated by protoc-gen-http. DO NOT EDIT.
// versions:
// protoc-gen-http v1.5.0
// protoc          v4.25.1
// source: shelf
package v1

import (
	context "context"
	json "encoding/json"
	gin "github.com/gin-gonic/gin"
	ecode "gitlab.papegames.com/fringe/sparrow/pkg/ecode"
	hooks "gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	server "gitlab.papegames.com/fringe/sparrow/pkg/server"
	xgin "gitlab.papegames.com/fringe/sparrow/pkg/server/xgin"
	xlog "gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func RegisterShelfServiceGinServer(s *xgin.Server, srv ShelfServiceServer) {
	eng := s.GetGinEngine()
	eng.Use(hooks.GetHandlerFunc()...)
	xgin.RegisterHandler(eng,
		"GET", "/v1/shelves/list",
		_ShelfServiceGin_ListShelf_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/shelves/list", "ListShelf")
	xgin.RegisterHandler(eng,
		"POST", "/v1/shelves",
		_ShelfServiceGin_CreateShelf_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/shelves", "CreateShelf")
	xgin.RegisterHandler(eng,
		"POST", "/v1/shelves/books",
		_ShelfServiceGin_CreateBook_Handler(srv, s.HTTPInterceptor()),
		"POST /v1/shelves/books", "CreateBook")
	xgin.RegisterHandler(eng,
		"GET", "/v1/shelves/books",
		_ShelfServiceGin_ListBook_Handler(srv, s.HTTPInterceptor()),
		"GET /v1/shelves/books", "ListBook")
}

func _ShelfServiceGin_ListShelf_Handler(srv ShelfServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/shelves/list",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ListShelf(ctx, in.(*ListShelfRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ListShelfRequest)
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ListShelf(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _ShelfServiceGin_CreateShelf_Handler(srv ShelfServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/shelves",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CreateShelf(ctx, in.(*CreateShelfRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(CreateShelfRequest)
		if c.Request == nil || c.Request.Body == nil {
			xlog.FromContext(ctx).Error("empty request body")
			return nil, ecode.BadRequest
		}
		in.Shelf = new(Shelf)
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in.Shelf); err != nil {
			xlog.FromContext(ctx).Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CreateShelf(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _ShelfServiceGin_CreateBook_Handler(srv ShelfServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "POST /v1/shelves/books",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.CreateBook(ctx, in.(*CreateBookRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(CreateBookRequest)
		if c.Request == nil || c.Request.Body == nil {
			xlog.FromContext(ctx).Error("empty request body")
			return nil, ecode.BadRequest
		}
		in.Book = new(Book)
		dec := json.NewDecoder(c.Request.Body)
		if err := dec.Decode(in.Book); err != nil {
			xlog.FromContext(ctx).Error("json.Decode with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.CreateBook(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}

func _ShelfServiceGin_ListBook_Handler(srv ShelfServiceServer, interceptor server.UnaryServerInterceptor) xgin.Handler {
	info := &server.UnaryServerInfo{
		Server:     srv,
		FullMethod: "GET /v1/shelves/books",
	}
	handler := func(ctx context.Context, in interface{}) (data interface{}, err error) {
		resp, verr := srv.ListBook(ctx, in.(*ListBookRequest))
		if resp != nil {
			data = resp
		}
		err = verr
		return
	}
	return func(c *gin.Context) (data interface{}, code ecode.Code) {
		ctx := xgin.Context(c)
		in := new(ListBookRequest)
		if err := xgin.DecodeValues(in, c); err != nil {
			xlog.FromContext(ctx).Error("xgin.DecodeValues with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if err := in.Validate(); err != nil {
			xlog.FromContext(ctx).Error("in.Validate with error",
				xlog.Err(err))
			return nil, ecode.Cause(ecode.BadRequest, err)
		}
		if interceptor == nil {
			resp, err := srv.ListBook(ctx, in)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		} else {
			resp, err := interceptor(ctx, in, info, handler)
			if resp != nil {
				data = resp
			}
			code = ecode.As(err)
			return
		}
	}
}
