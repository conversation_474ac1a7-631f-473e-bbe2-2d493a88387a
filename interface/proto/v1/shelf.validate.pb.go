// Code generated by protoc-gen-validate. DO NOT EDIT.
// versions:
// protoc-gen-validate v1.0.5
// protoc              v4.25.1
// source: interface/proto/v1/shelf.proto

package v1

func (x *Shelf) Validate() error {
	if len(x.GetTheme()) == 0 {
		return ShelfValidationError{
			field:   "Theme",
			reason:  "required",
			message: "value is required",
		}
	}
	if len(x.GetTheme()) < 1 {
		return ShelfValidationError{
			field:   "Theme",
			reason:  "min_length",
			message: "value must be at least 1 bytes",
		}
	}
	if len(x.GetTheme()) > 64 {
		return ShelfValidationError{
			field:   "Theme",
			reason:  "max_length",
			message: "value length must be at most 64 bytes",
		}
	}
	return nil
}

func (x *ListShelfRequest) Validate() error {
	return nil
}

func (x *ListShelfResponse) Validate() error {
	for _, item := range x.GetShelves() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListShelfResponseValidationError{
					field:   "Shelves",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

func (x *CreateShelfRequest) Validate() error {
	if x.GetShelf() == nil {
		return CreateShelfRequestValidationError{
			field:   "Shelf",
			reason:  "required",
			message: "value is required",
		}
	}
	if v, ok := interface{}(x.GetShelf()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateShelfRequestValidationError{
				field:   "Shelf",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *Book) Validate() error {
	return nil
}

func (x *CreateBookRequest) Validate() error {
	if x.GetBook() == nil {
		return CreateBookRequestValidationError{
			field:   "Book",
			reason:  "required",
			message: "value is required",
		}
	}
	if v, ok := interface{}(x.GetBook()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBookRequestValidationError{
				field:   "Book",
				reason:  "embedded",
				message: "embedded message failed validation",
				cause:   err,
			}
		}
	}
	return nil
}

func (x *ListBookRequest) Validate() error {
	return nil
}

func (x *ListBookResponse) Validate() error {
	for _, item := range x.GetBooks() {
		if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListBookResponseValidationError{
					field:   "Books",
					reason:  "embedded",
					message: "embedded message failed validation",
					cause:   err,
				}
			}
		}
	}
	return nil
}

type ShelfValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ShelfValidationError) Field() string { return e.field }

func (e ShelfValidationError) Reason() string { return e.reason }

func (e ShelfValidationError) Message() string { return e.message }

func (e ShelfValidationError) Cause() error { return e.cause }

func (e ShelfValidationError) ErrorName() string { return "ShelfValidationError" }

func (e ShelfValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Shelf." + e.field + ": " + e.message + cause
}

type ListShelfRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ListShelfRequestValidationError) Field() string { return e.field }

func (e ListShelfRequestValidationError) Reason() string { return e.reason }

func (e ListShelfRequestValidationError) Message() string { return e.message }

func (e ListShelfRequestValidationError) Cause() error { return e.cause }

func (e ListShelfRequestValidationError) ErrorName() string { return "ListShelfRequestValidationError" }

func (e ListShelfRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ListShelfRequest." + e.field + ": " + e.message + cause
}

type ListShelfResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ListShelfResponseValidationError) Field() string { return e.field }

func (e ListShelfResponseValidationError) Reason() string { return e.reason }

func (e ListShelfResponseValidationError) Message() string { return e.message }

func (e ListShelfResponseValidationError) Cause() error { return e.cause }

func (e ListShelfResponseValidationError) ErrorName() string {
	return "ListShelfResponseValidationError"
}

func (e ListShelfResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ListShelfResponse." + e.field + ": " + e.message + cause
}

type CreateShelfRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CreateShelfRequestValidationError) Field() string { return e.field }

func (e CreateShelfRequestValidationError) Reason() string { return e.reason }

func (e CreateShelfRequestValidationError) Message() string { return e.message }

func (e CreateShelfRequestValidationError) Cause() error { return e.cause }

func (e CreateShelfRequestValidationError) ErrorName() string {
	return "CreateShelfRequestValidationError"
}

func (e CreateShelfRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CreateShelfRequest." + e.field + ": " + e.message + cause
}

type BookValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e BookValidationError) Field() string { return e.field }

func (e BookValidationError) Reason() string { return e.reason }

func (e BookValidationError) Message() string { return e.message }

func (e BookValidationError) Cause() error { return e.cause }

func (e BookValidationError) ErrorName() string { return "BookValidationError" }

func (e BookValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid Book." + e.field + ": " + e.message + cause
}

type CreateBookRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e CreateBookRequestValidationError) Field() string { return e.field }

func (e CreateBookRequestValidationError) Reason() string { return e.reason }

func (e CreateBookRequestValidationError) Message() string { return e.message }

func (e CreateBookRequestValidationError) Cause() error { return e.cause }

func (e CreateBookRequestValidationError) ErrorName() string {
	return "CreateBookRequestValidationError"
}

func (e CreateBookRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid CreateBookRequest." + e.field + ": " + e.message + cause
}

type ListBookRequestValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ListBookRequestValidationError) Field() string { return e.field }

func (e ListBookRequestValidationError) Reason() string { return e.reason }

func (e ListBookRequestValidationError) Message() string { return e.message }

func (e ListBookRequestValidationError) Cause() error { return e.cause }

func (e ListBookRequestValidationError) ErrorName() string { return "ListBookRequestValidationError" }

func (e ListBookRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ListBookRequest." + e.field + ": " + e.message + cause
}

type ListBookResponseValidationError struct {
	field   string
	reason  string
	message string
	cause   error
}

func (e ListBookResponseValidationError) Field() string { return e.field }

func (e ListBookResponseValidationError) Reason() string { return e.reason }

func (e ListBookResponseValidationError) Message() string { return e.message }

func (e ListBookResponseValidationError) Cause() error { return e.cause }

func (e ListBookResponseValidationError) ErrorName() string { return "ListBookResponseValidationError" }

func (e ListBookResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = " | caused by: " + e.cause.Error()
	}
	return "invalid ListBookResponse." + e.field + ": " + e.message + cause
}
