# Generated with protoc-gen-openapi
# https://gitlab.papegames.com/fringe/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ShelfService API
    description: This API represents shelf service.
    version: 0.0.1
servers:
    - url: https://shelf.papegames.com
paths:
    /v1/shelves:
        post:
            tags:
                - ShelfService
            description: Creates a shelf, and returns the new Shelf.
            operationId: CreateShelf
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Shelf'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/Shelf'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/shelves/books:
        get:
            tags:
                - ShelfService
            description: List books in a shelf.
            operationId: ListBook
            parameters:
                - name: shelf_id
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: page_size
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/ListBookResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
        post:
            tags:
                - ShelfService
            description: Creates a Book to a Shelf.
            operationId: CreateBook
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Book'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/Book'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
    /v1/shelves/list:
        get:
            tags:
                - ShelfService
            description: List shelves.
            operationId: ListShelf
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    code:
                                        type: integer
                                        format: int32
                                    info:
                                        example: ok
                                        type: string
                                    request_id:
                                        example: 16vHbfABAd
                                        type: string
                                    data:
                                        $ref: '#/components/schemas/ListShelfResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/_failReturn'
components:
    schemas:
        Book:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                shelf_id:
                    type: integer
                    format: int64
                create_time:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/Timestamp'
                update_time:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/Timestamp'
        ListBookResponse:
            type: object
            properties:
                books:
                    type: array
                    items:
                        $ref: '#/components/schemas/Book'
                next_page_token:
                    type: string
        ListShelfResponse:
            type: object
            properties:
                shelves:
                    type: array
                    items:
                        $ref: '#/components/schemas/Shelf'
            description: Response message for ShelfService.ListShelf.
        Shelf:
            required:
                - theme
            type: object
            properties:
                name:
                    readOnly: true
                    type: string
                    description: The resource name of the shelf. Shelf names have the form `shelves/{shelf_id}`. The name is ignored when creating a shelf.
                theme:
                    maxLength: 64
                    minLength: 1
                    type: string
                    description: The theme of the shelf
                create_time:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/Timestamp'
                    description: The creation date and time.
                update_time:
                    readOnly: true
                    allOf:
                        - $ref: '#/components/schemas/Timestamp'
                    description: The last update date and time.
            description: A Shelf contains a collection of books with a theme.
        Timestamp:
            type: string
            format: date-time
        _failReturn:
            type: object
            properties:
                code:
                    example: 400
                    type: integer
                    format: int32
                info:
                    example: error
                    type: string
                request_id:
                    example: 16vHbfABAd
                    type: string
tags:
    - name: ShelfService
