version: "2"

run:
  timeout: 10m

linters:
  enable:
    - bodyclose
    - staticcheck
    - goprintffuncname
    - importas
    - misspell
    - nakedret
    - nilerr
    - nolintlint
    - prealloc
    - rowserrcheck
    - unconvert
    - govet
  disable:
    - errcheck

  settings:
    staticcheck:
      checks:
        - all
        - "-ST1000"
        - "-ST1003"
        - "-ST1005"
        - "-ST1016"
        - "-ST1020"
        - "-ST1021"
        - "-ST1022"
        - "-QF1001"
        - "-QF1008"

    misspell:
      locale: US
      ignore-rules:
        - creater
        - cancelled
        - governer

  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$

formatters:
  enable:
    - gofmt
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
