package assembler

import (
	"shelf/domain/entity"
	v1 "shelf/interface/proto/v1"

	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

func FromShelves(shelf []*entity.Shelf) []*v1.Shelf {
	shelves := make([]*v1.Shelf, len(shelf))
	for i, s := range shelf {
		shelves[i] = FromShelf(s)
	}
	return shelves
}

func FromShelf(shelf *entity.Shelf) *v1.She<PERSON> {
	return &v1.She<PERSON>{
		Name:       shelf.Name,
		Theme:      shelf.Theme,
		CreateTime: xtype.NewTimestamp(shelf.CreateTime),
		UpdateTime: xtype.NewTimestamp(shelf.UpdateTime),
	}
}

func ToShelf(shelf *v1.Shelf) *entity.Shelf {
	return &entity.Shelf{
		Name:  shelf.Name,
		Theme: shelf.Theme,
	}
}
