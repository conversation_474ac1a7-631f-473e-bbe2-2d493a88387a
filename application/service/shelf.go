package service

import (
	"context"

	"shelf/application/assembler"
	v1 "shelf/interface/proto/v1"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func (app *appService) CreateShelf(ctx context.Context, request *v1.CreateShelfRequest) (*v1.Shelf, error) {
	shelf := assembler.ToShelf(request.Shelf)
	shelf, err := app.shelf.CreateShelf(ctx, shelf)
	if err != nil {
		xlog.FromContext(ctx).Error("CreateShelf with error",
			xlog.Err(err))
		return nil, err
	}
	return assembler.FromShelf(shelf), nil
}

func (app *appService) ListShelf(ctx context.Context, request *v1.ListShelfRequest) (*v1.ListShelfResponse, error) {
	shelves, err := app.shelf.ListShelf(ctx)
	if err != nil {
		xlog.FromContext(ctx).Error("ListShelf with error",
			xlog.Err(err))
		return nil, err
	}
	var resp v1.ListShelfResponse
	resp.Shelves = assembler.FromShelves(shelves)
	return &resp, nil
}

func (app *appService) ListBook(ctx context.Context, request *v1.ListBookRequest) (*v1.ListBookResponse, error) {
	books, err := app.shelf.ListShelfBooks(ctx, request.ShelfId)
	if err != nil {
		xlog.FromContext(ctx).Error("ListBook with error",
			xlog.Err(err))
		return nil, err
	}
	var resp v1.ListBookResponse
	resp.Books = assembler.FromBooks(books)
	return &resp, nil
}
