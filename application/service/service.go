package service

import (
	"shelf/domain/service"

	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/ioc"
)

var svc = NewAppService()

func AppService() *appService {
	return svc
}

type appService struct {
	shelf service.ShelfService
	book  service.BookService
}

func NewAppService() *appService {
	return &appService{
		shelf: ioc.MustInvoke[service.ShelfService](),
		book:  ioc.MustInvoke[service.BookService](),
	}
}
