package service

import (
	"context"
	"shelf/domain/entity"
	v1 "shelf/interface/proto/v1"

	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

func (app *appService) CreateBook(ctx context.Context, request *v1.CreateBookRequest) (*v1.Book, error) {
	book, err := app.book.CreateBook(ctx, (*entity.Book)(request.Book))
	if err != nil {
		xlog.FromContext(ctx).Error("CreateShelf with error",
			xlog.Err(err))
		return nil, err
	}
	return (*v1.Book)(book), nil
}
