package service

import (
	"context"
	"shelf/domain/entity"
	"shelf/domain/repository"

	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/ioc"
)

func init() {
	ioc.ProvideFunc[BookService](NewBookService)
}

type BookService interface {
	CreateBook(ctx context.Context, book *entity.Book) (*entity.Book, error)
}

type bookService struct {
	bookRepo repository.BookRepository
}

func NewBookService() BookService {
	return &bookService{
		bookRepo: ioc.MustInvoke[repository.BookRepository](),
	}
}

func (s *bookService) CreateBook(ctx context.Context, book *entity.Book) (*entity.Book, error) {
	// TODO: validation
	return s.bookRepo.CreateBook(ctx, book)
}
