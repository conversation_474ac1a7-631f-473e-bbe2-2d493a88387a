package service

import (
	"context"

	"shelf/domain/entity"
	"shelf/domain/repository"

	"gitlab.papegames.com/fringe/sparrow/pkg/pattern/ioc"
)

func init() {
	ioc.ProvideFunc[ShelfService](NewShelfService)

}

type ShelfService interface {
	// Creates a shelf, and returns the new Shelf.
	CreateShelf(ctx context.Context, shelf *entity.Shelf) (*entity.Shelf, error)
	// List shelves.
	ListShelf(ctx context.Context) ([]*entity.Shelf, error)
	ListShelfBooks(ctx context.Context, shelfID int64) ([]*entity.Book, error)
}

type shelfService struct {
	shelfRepo repository.ShelfRepository
}

func NewShelfService() ShelfService {
	return &shelfService{
		shelfRepo: ioc.MustInvoke[repository.ShelfRepository](),
	}
}

func (s *shelfService) CreateShelf(ctx context.Context, shelf *entity.Shelf) (*entity.Shelf, error) {
	// TODO: validation
	return s.shelfRepo.CreateShelf(ctx, shelf)
}

func (s *shelfService) ListShelf(ctx context.Context) ([]*entity.Shelf, error) {
	// TODO: filter & pagination
	return s.shelfRepo.ListShelf(ctx)
}

func (s *shelfService) ListShelfBooks(ctx context.Context, shelfID int64) ([]*entity.Book, error) {
	// TODO: filter & pagination
	return s.shelfRepo.ListShelfBooks(ctx, shelfID)
}
