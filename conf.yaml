# service
host_http: ""
host_grpc: ""
register: true

sparrow:
  log:
    file: "./logs/shelf"
    encoding: "console"
    rotate: "hourly"
    buffer: 4096

  govern:
    enable: true
    host: "internal:0"

  registrar:
    endpoints:
      - "127.0.0.1:2379"

  database:
    mysql:
      dataSource: "root:nikki@(127.0.0.1:3306)/shelf?timeout=5s&parseTime=true&loc=Local&charset=utf8"
      maxIdleConns: 5
      maxOpenConns: 5
