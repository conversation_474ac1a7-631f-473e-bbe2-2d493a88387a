FROM golang:1.24-alpine as builder

RUN apk update --no-cache \
 && apk add --no-cache git make openssh \
 && go env -w GONOPROXY="gitlab.papegames.com" \
 && go env -w GONOSUMDB="gitlab.papegames.com" \
 && go env -w GOPROXY="https://goproxy.cn,direct"

WORKDIR /build

ENV GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no"

RUN echo -ne " \
[url \"ssh://************************/\"] \
    insteadOf = https://gitlab.papegames.com/ \
" > /root/.gitconfig

COPY --chmod=0600 gitlab /root/.ssh/id_rsa

COPY . .

RUN \
    --mount=type=cache,target=/go/pkg/mod \
    make docker

FROM alpine

WORKDIR /app/

RUN apk update --no-cache \
 && apk add --no-cache ca-certificates \
 && apk add --no-cache tzdata

ENV TZ Asia/Shanghai

COPY --from=0 /build/shelf shelf

CMD ["./shelf"]

EXPOSE 8090
